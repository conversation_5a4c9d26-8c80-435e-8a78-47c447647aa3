
import React from 'react';
import { ExternalLink, Eye } from 'lucide-react';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface ForYouWidgetProps {
  relatedPosts: RelatedPost[];
  selectedPostIndex: number | null;
  onTogglePost: (index: number) => void;
  onOpenPost: (url: string, e: React.MouseEvent) => void;
}

const ForYouWidget: React.FC<ForYouWidgetProps> = ({
  relatedPosts,
  selectedPostIndex,
  onTogglePost,
  onOpenPost
}) => {

  // Function to get random 5 posts from the available posts
  const getRandomPosts = (posts: RelatedPost[], count: number = 5) => {
    if (posts.length <= count) return posts;

    const shuffled = [...posts].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  };

  const displayPosts = getRandomPosts(relatedPosts);

  return (
    <div className="space-y-4">{/* Posts List - Reduced spacing */}
        {displayPosts.map((post, index) => (
          <div
            key={index}
            className={`bg-gradient-to-br from-white to-gray-50/30 border rounded-xl overflow-hidden shadow-md transition-all duration-300 fade-in-up hover:shadow-lg ${
              selectedPostIndex === index
                ? 'border-green-500/80 shadow-xl ring-1 ring-green-500/30'
                : 'border-gray-200/80 hover:border-gray-300/80'
            }`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Post Header - Compact design */}
            <div className="p-3 sm:p-4">
              <div className="flex items-start gap-3">
                <div className="flex-1">
                  {/* Title with embedded link arrow - Only title is clickable */}
                  <div
                    className="flex items-start gap-2 mb-2 group cursor-pointer hover:bg-gray-50/50 p-1 -m-1 rounded-lg transition-colors"
                    onClick={(e) => onOpenPost(post.post_url, e)}
                  >
                    <h4 className="font-semibold text-gray-700 text-sm sm:text-base leading-tight line-clamp-2 group-hover:text-green-600 transition-colors">
                      {post.post_title}
                    </h4>
                    <ExternalLink
                      size={14}
                      className="text-gray-400 group-hover:text-green-500 transition-colors flex-shrink-0 mt-0.5"
                    />
                  </div>

                  {/* View Details Button - Compact design */}
                  <button
                    onClick={() => onTogglePost(index)}
                    className="flex items-center gap-1.5 px-2.5 py-1.5 bg-gray-100 hover:bg-green-50 hover:text-green-700 text-gray-600 rounded-lg transition-colors text-xs sm:text-sm font-medium"
                  >
                    <Eye size={12} />
                    <span className="hidden sm:inline">
                      {selectedPostIndex === index ? 'Close Details' : 'View Details'}
                    </span>
                    <span className="sm:hidden">
                      {selectedPostIndex === index ? 'Close' : 'View'}
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}

        {relatedPosts.length === 0 && (
          <div className="text-center py-12 px-4">
            <div className="relative mb-4">
              <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto shadow-md">
                <Eye size={24} className="text-gray-400" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-green-200/40 to-green-200/30 rounded-full"></div>
            </div>
            <h3 className="text-gray-600 text-lg font-semibold mb-2">No related posts found</h3>
            <p className="text-gray-400 text-sm max-w-xs mx-auto leading-relaxed">
              We're always adding new content. Check back later for personalized recommendations!
            </p>
          </div>
        )}
    </div>
  );
};

export default ForYouWidget;
