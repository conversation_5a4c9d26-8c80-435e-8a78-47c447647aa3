
import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import ForYouIcon from './icons/ForYouIcon';
import NewsSummaryWidget from './NewsSummaryWidget';
import ForYouWidget from './ForYouWidget';
import AiSummaryIcon from './icons/AiSummaryIcon';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface NewsData {
  post_id: string;
  main_post_summary: string;
  related_posts: RelatedPost[];
}

interface NewsWidgetProps {
  mode: 'summary' | 'related' | null;
  onClose: () => void;
  selectedPostIndex?: number | null;
  onTogglePost?: (index: number) => void;
  onOpenPost?: (url: string, e: React.MouseEvent) => void;
}

const NewsWidget: React.FC<NewsWidgetProps> = ({
  mode,
  onClose,
  selectedPostIndex,
  onTogglePost,
  onOpenPost
}) => {
  const [newsData, setNewsData] = useState<NewsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [isClosing, setIsClosing] = useState(false);
  const contentRef = React.useRef<HTMLDivElement>(null);



  // Calculate optimal widget size based on content
  const getOptimalWidth = () => {
    // Fixed 600px width for summary, 400px for related mode, responsive on smaller screens
    return mode === 'summary' ? 'min(95vw, 600px)' : 'min(95vw, 400px)';
  };

  const getOptimalHeight = () => {
    // For summary mode, fit content with max height
    if (mode === 'summary') {
      return 'auto';
    }
    return 'min(800px, 90vh)'; // Fixed height for related posts
  };

  // Real API call to fetch news data
  const fetchNewsData = async () => {
    setLoading(true);
    try {
      // Always use local fallback for now to ensure data is loaded
      const fallbackResponse = await fetch('/news-api.json');
      if (fallbackResponse.ok) {
        const fallbackData: NewsData = await fallbackResponse.json();
        setNewsData(fallbackData);
      } else {
        throw new Error('Fallback API failed');
      }
    } catch (error) {
      console.error('Error fetching news data:', error);
      // Set empty data or show error state
      setNewsData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      setIsClosing(false);
    }, 300);
  };





  useEffect(() => {
    if (mode && !newsData) {
      setLoading(true);
      fetchNewsData();
    }
  }, [mode, newsData]);

  if (!mode) return null;

  return (
    <>
      {/* Widget Container */}
      <div
        className={`fixed z-[9999]
                   bg-white shadow-2xl border border-gray-200
                   transform transition-all duration-500 ease-out
                   ${mode && !isClosing
                     ? (mode === 'summary' ? 'translate-y-0 opacity-100 scale-100 widget-entrance' : 'translate-x-0 opacity-100')
                     : (mode === 'summary' ? 'translate-y-full opacity-0 scale-95' : '-translate-x-full opacity-0')
                   }
                   content-responsive mobile-optimized floating-widget ${mode === 'summary' ? 'widget-summary rounded-xl sm:rounded-2xl' : 'widget-related h-full'}`}
        style={{
          ...(mode === 'summary' ? {
            bottom: '160px', // Position above the icon with more clearance (icon is at bottom: 24px + icon height ~80px + gap)
            right: '1rem',
            width: getOptimalWidth(),
            height: getOptimalHeight(),
            maxHeight: 'calc(100vh - 200px)', // Leave space for icon below
            minHeight: 'auto',
          } : {
            top: 0,
            right: 0,
            width: getOptimalWidth(),
            height: '100vh',
            maxHeight: '100vh',
            minHeight: '100vh',
          }),
          background: mode === 'summary'
            ? 'linear-gradient(135deg, rgba(34,197,94,0.03) 0%, rgba(255,255,255,0.98) 20%, rgba(249,250,251,1) 100%)'
            : 'linear-gradient(135deg, rgba(34,197,94,0.03) 0%, rgba(255,255,255,0.98) 20%, rgba(249,250,251,1) 100%)',
          boxShadow: mode === 'summary'
            ? '0 25px 50px -12px rgba(34, 197, 94, 0.15), 0 0 0 2px rgba(34, 197, 94, 0.15), 0 8px 32px -8px rgba(0, 0, 0, 0.2)'
            : '0 25px 50px -12px rgba(34, 197, 94, 0.15), 0 0 0 2px rgba(34, 197, 94, 0.15), 0 8px 32px -8px rgba(0, 0, 0, 0.2)'
        }}
      >
        {/* Modern Header */}
        <div className={`relative p-4 sm:p-6 pb-2 sm:pb-3 flex justify-between items-start border-b ${
          mode === 'summary' ? 'border-gray-300/80' : 'border-gray-300/80'
        }`}>
          <div className="flex items-center gap-3">
            <div className={`p-3 rounded-xl shadow-sm ${
              mode === 'summary'
                ? 'bg-gradient-to-br from-green-500 to-green-600 text-white'
                : 'bg-gradient-to-br from-green-500 to-green-600 text-white'
            }`}>
              {mode === 'summary' ? (
                // <Sparkles className="w-4 h-4 sm:w-5 sm:h-5" />
                <AiSummaryIcon size={20} className="sm:w-5 sm:h-5" />
              ) : (
                <ForYouIcon size={20} className="sm:w-5 sm:h-5" />
              )}
            </div>
            <div>
              <h3 className={`font-bold text-base sm:text-lg ${
                mode === 'summary' ? 'text-gray-800' : 'text-gray-800'
              }`}>
                {mode === 'summary' ? 'Article Summary' : 'For You'}
              </h3>
              <p className={`text-xs sm:text-sm ${
                mode === 'summary' ? 'text-green-600' : 'text-green-600'
              }`}>
                {mode === 'summary' ? 'Powered By Next AI' : `${newsData?.related_posts?.length || 0} related posts curated by Next AI`}
              </p>
            </div>
          </div>

          <button
            onClick={handleClose}
            className={`p-2 rounded-lg transition-all duration-200 hover:rotate-90 touch-target ${
              mode === 'summary'
                ? 'hover:bg-green-50 text-gray-400 hover:text-green-600'
                : 'hover:bg-green-50 text-gray-400 hover:text-green-600'
            }`}
            title="Close"
          >
            <X size={16} className="sm:w-4 sm:h-4" />
          </button>
        </div>

        {/* Content */}
        <div
          ref={contentRef}
          className={mode === 'summary' ? 'overflow-hidden' : 'flex-1 overflow-hidden'}
        >
          <div className={mode === 'summary' ? 'smart-scroll mobile-content' : 'h-full smart-scroll mobile-content'}>
            <div className={mode === 'summary' ? 'relative' : 'relative min-h-[200px]'}>
              {loading ? (
                <div className="absolute inset-0 flex items-center justify-center p-6 sm:p-8 lg:p-12">
                  <div className="text-center">
                    <div className="relative mx-auto mb-4 sm:mb-6 w-12 h-12 sm:w-16 sm:h-16">
                      <div className={`absolute inset-0 rounded-full border-4 ${
                        mode === 'summary' ? 'border-gray-100' : 'border-gray-100'
                      }`}></div>
                      <div className={`absolute inset-0 rounded-full border-4 border-t-transparent animate-spin ${
                        mode === 'summary' ? 'border-green-500' : 'border-green-500'
                      }`}></div>
                    </div>
                    <p className={`text-base sm:text-lg font-medium ${
                      mode === 'summary' ? 'text-gray-800' : 'text-gray-800'
                    }`}>Loading content...</p>
                    <p className={`text-sm mt-2 ${
                      mode === 'summary' ? 'text-green-600' : 'text-green-600'
                    }`}>Please wait while we fetch the data</p>
                  </div>
                </div>
              ) : newsData ? (
                <div className="p-4 sm:p-6 mobile-optimized adaptive-content transition-all duration-300">
                  {mode === 'summary' && (
                    <NewsSummaryWidget
                      summary={newsData.main_post_summary}
                      postId={newsData.post_id}
                      isExpanded={true}
                      onToggle={() => {}}
                    />
                  )}

                  {mode === 'related' && (
                    <ForYouWidget
                      relatedPosts={newsData.related_posts}
                      selectedPostIndex={selectedPostIndex !== undefined ? selectedPostIndex : null}
                      onTogglePost={onTogglePost || (() => {})}
                      onOpenPost={onOpenPost || (() => {})}
                    />
                  )}
                </div>
              ) : (
                <div className="absolute inset-0 flex items-center justify-center p-6 sm:p-8 lg:p-12">
                  <div className="text-center">
                    <div className={`mb-4 ${
                      mode === 'summary' ? 'text-green-300' : 'text-gray-400'
                    }`}>
                      <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <p className={`text-base sm:text-lg font-medium ${
                      mode === 'summary' ? 'text-gray-800' : 'text-[#4C4C4C]'
                    }`}>No content available</p>
                    <p className={`text-sm mt-2 ${
                      mode === 'summary' ? 'text-green-600' : 'text-gray-500'
                    }`}>Unable to load data at this time</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default NewsWidget;
