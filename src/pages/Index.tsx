
import { useState, useEffect } from 'react';
import { Sparkles } from 'lucide-react';
import ForYouIcon from '../components/icons/ForYouIcon';
import NewsLayout from '../components/NewsLayout';
import NewsWidget from '../components/NewsWidget';
import NewsDetailsDialog from '../components/NewsDetailsDialog';
import AiSummaryIcon from '@/components/icons/AiSummaryIcon';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface NewsData {
  post_id: string;
  main_post_summary: string;
  related_posts: RelatedPost[];
}

const Index = () => {
  const [widgetMode, setWidgetMode] = useState<'summary' | 'related' | null>('summary');
  const [newsData, setNewsData] = useState<NewsData | null>(null);

  // State for details dialog
  const [selectedPostIndex, setSelectedPostIndex] = useState<number | null>(null);
  const [expandedQAs, setExpandedQAs] = useState<string[]>([]);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Create a unique key for this session based on post titles
  const sessionKey = newsData ? `for-you-expanded-${newsData.related_posts.map(p => p.post_title).join('').slice(0, 50)}` : '';

  // Fetch news data
  useEffect(() => {
    const fetchNewsData = async () => {
      try {
        const fallbackResponse = await fetch('/news-api.json');
        if (fallbackResponse.ok) {
          const fallbackData: NewsData = await fallbackResponse.json();
          setNewsData(fallbackData);
        }
      } catch (error) {
        console.error('Error fetching news data:', error);
        setNewsData(null);
      }
    };

    if (widgetMode === 'related' && !newsData) {
      fetchNewsData();
    }
  }, [widgetMode, newsData]);

  // Initialize expanded state on first load
  useEffect(() => {
    if (!hasInitialized && newsData?.related_posts && newsData.related_posts.length > 0) {
      // Try to load saved state from sessionStorage
      const savedState = sessionStorage.getItem(sessionKey);
      if (savedState) {
        try {
          const parsed = JSON.parse(savedState);
          setSelectedPostIndex(parsed.selectedPostIndex || null);
          setExpandedQAs(parsed.expandedQAs || []);
        } catch (error) {
          console.warn('Failed to parse saved state:', error);
          setSelectedPostIndex(null);
        }
      } else {
        setSelectedPostIndex(null);
      }
      setHasInitialized(true);
    }
  }, [newsData, sessionKey, hasInitialized]);

  // Save state to sessionStorage whenever it changes
  useEffect(() => {
    if (hasInitialized && sessionKey) {
      const stateToSave = {
        selectedPostIndex,
        expandedQAs
      };
      sessionStorage.setItem(sessionKey, JSON.stringify(stateToSave));
    }
  }, [selectedPostIndex, expandedQAs, sessionKey, hasInitialized]);

  // Callback functions for ForYouWidget and NewsDetailsDialog
  const handleTogglePost = (index: number) => {
    if (selectedPostIndex === index) {
      handleCloseDetails();
    } else {
      setSelectedPostIndex(index);
      setExpandedQAs(prevQAs => prevQAs.filter(qa => qa.startsWith(`${index}-`)));
    }
  };

  const handleToggleQA = (questionId: string) => {
    setExpandedQAs(prev => {
      if (prev.includes(questionId)) {
        return prev.filter(qa => qa !== questionId);
      } else {
        return [...prev, questionId];
      }
    });
  };

  const handleCloseDetails = () => {
    setSelectedPostIndex(null);
    setExpandedQAs([]);
  };

  const handleOpenPost = (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(url, '_blank');
  };

  return (
    <div className="relative">
      <NewsLayout />
      
      {/* Control Icons - always show */}
      <div className="fixed bottom-4 right-4 sm:bottom-6 sm:right-6 z-50 flex flex-col gap-3 sm:gap-4">
          {/* AI Summary Button */}
          <div className="relative group">
            <button
              onClick={() => setWidgetMode(widgetMode === 'summary' ? null : 'summary')}
              className="relative p-4 sm:p-5 rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                         bg-gradient-to-br from-green-500 to-green-600 text-white
                         pulse-glow float-animation hover:shadow-[0_0_40px_rgba(34,197,94,1)]
                         active:scale-95 group-hover:rotate-12 touch-target mobile-button"
              // title="AI Summary"
            >
              {/* <Sparkles size={24} className="sm:w-7 sm:h-7 drop-shadow-lg" /> */}
              <AiSummaryIcon size={24} className="sm:w-7 sm:h-7" />
            </button>

            {/* Tooltip */}
            <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 invisible group-hover:visible
                            transition-opacity duration-0 pointer-events-none hidden sm:block">
              <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap
                              shadow-lg border border-gray-700">
                AI Summary
                <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
              </div>
            </div>
          </div>

          {/* Related Posts Button */}
          <div className="relative group">
            <button
              onClick={() => setWidgetMode(widgetMode === 'related' ? null : 'related')}
              className="relative p-4 sm:p-5 rounded-full shadow-2xl transition-all duration-500 hover:scale-110
                         bg-gradient-to-br from-gray-500 to-gray-600 text-white
                         pulse-glow float-animation hover:shadow-[0_0_40px_rgba(107,114,128,1)]
                         active:scale-95 group-hover:rotate-12 touch-target mobile-button"
              // title="For You"
              style={{ animationDelay: '1s' }}
            >
              <ForYouIcon size={24} className="sm:w-7 sm:h-7 drop-shadow-lg" />
            </button>

            {/* Tooltip */}
            <div className="absolute right-full mr-3 top-1/2 -translate-y-1/2 invisible group-hover:visible
                            transition-opacity duration-0 pointer-events-none hidden sm:block">
              <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap
                              shadow-lg border border-gray-700">
                For You
                <div className="absolute left-full top-1/2 -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
              </div>
            </div>
          </div>
        </div>
      
      {/* NewsDetailsDialog - Large screens: side-by-side positioning */}
      {widgetMode === 'related' && newsData && selectedPostIndex !== null && (
        <div
          className={`fixed top-1/2 -translate-y-1/2 z-[10000] hidden md:block transition-all duration-500 ease-out ${
            selectedPostIndex !== null
              ? 'opacity-100 translate-x-0'
              : 'opacity-0 translate-x-8 pointer-events-none'
          }`}
          style={{
            // Position to the left of NewsWidget with 26px gap
            right: 'calc(min(95vw, 580px) + 26px)',
            width: 'min(500px, calc(100vw - min(95vw, 400px) - 52px))', // Wider
            height: 'min(800px, 85vh)', // Taller
          }}
        >
          <NewsDetailsDialog
            selectedPost={newsData.related_posts[selectedPostIndex]}
            selectedPostIndex={selectedPostIndex}
            expandedQAs={expandedQAs}
            onClose={handleCloseDetails}
            onToggleQA={handleToggleQA}
            onOpenPost={handleOpenPost}
          />
        </div>
      )}

      {/* NewsDetailsDialog - Mobile screens: centered positioning */}
      {widgetMode === 'related' && newsData && selectedPostIndex !== null && (
        <div
          className={`fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-[10000] md:hidden transition-all duration-500 ease-out ${
            selectedPostIndex !== null
              ? 'opacity-100 scale-100'
              : 'opacity-0 scale-95 pointer-events-none'
          }`}
          style={{
            width: 'min(450px, 90vw)',
            height: 'min(600px, 80vh)',
          }}
        >
          <NewsDetailsDialog
            selectedPost={newsData.related_posts[selectedPostIndex]}
            selectedPostIndex={selectedPostIndex}
            expandedQAs={expandedQAs}
            onClose={handleCloseDetails}
            onToggleQA={handleToggleQA}
            onOpenPost={handleOpenPost}
          />
        </div>
      )}

      <NewsWidget
        mode={widgetMode}
        onClose={() => setWidgetMode(null)}
        selectedPostIndex={selectedPostIndex}
        onTogglePost={handleTogglePost}
        onOpenPost={handleOpenPost}
      />
    </div>
  );
};

export default Index;
